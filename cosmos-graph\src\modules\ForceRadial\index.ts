import regl from 'regl'
import { GraphConfig } from '@/graph/config'
import { Store } from '@/graph/modules/Store'
import { GraphData } from '@/graph/modules/GraphData'
import { Points } from '@/graph/modules/Points'
import { CoreModule } from '@/graph/modules/core-module'

export interface RadialGroup {
  indices: number[]
  radius: number
  centerX?: number
  centerY?: number
  strength?: number
}

export class ForceRadial extends CoreModule {
  private runCommand: regl.DrawCommand | undefined
  private radialFbo: regl.Framebuffer2D | undefined
  private radialTexture: regl.Texture2D | undefined
  private radialGroups: Map<number, RadialGroup> = new Map()
  private radialData: Float32Array | undefined
  
  public setRadialGroups (groups: RadialGroup[]): void {
    this.radialGroups.clear()
    groups.forEach((group, groupIndex) => {
      this.radialGroups.set(groupIndex, group)
    })
    this.updateRadialTexture()
  }
  
  public addRadialGroup (group: RadialGroup): number {
    const index = this.radialGroups.size
    this.radialGroups.set(index, group)
    this.updateRadialTexture()
    return index
  }
  
  public removeRadialGroup (index: number): void {
    this.radialGroups.delete(index)
    this.updateRadialTexture()
  }
  
  public clearRadialGroups (): void {
    this.radialGroups.clear()
    this.updateRadialTexture()
  }
  
  private updateRadialTexture (): void {
    if (!this.store.pointsTextureSize || !this.radialData) return
    
    // Reset radial data
    this.radialData.fill(0)
    
    // Fill radial data for each point
    this.radialGroups.forEach((group) => {
      const centerX = group.centerX ?? 0
      const centerY = group.centerY ?? 0
      const radius = group.radius
      const strength = group.strength ?? 1.0
      
      group.indices.forEach(pointIndex => {
        if (pointIndex < this.radialData.length / 4) {
          const offset = pointIndex * 4
          this.radialData[offset] = centerX
          this.radialData[offset + 1] = centerY
          this.radialData[offset + 2] = radius
          this.radialData[offset + 3] = strength
        }
      })
    })
    
    // Update texture
    if (this.radialTexture) {
      this.radialTexture({
        data: this.radialData,
        shape: [this.store.pointsTextureSize * this.store.pointsTextureSize, 1, 4],
        type: 'float',
      })
    }
  }
  
  public create (): void {
    if (!this.store.pointsTextureSize) return
    
    const size = this.store.pointsTextureSize * this.store.pointsTextureSize
    this.radialData = new Float32Array(size * 4)
    
    // Create texture for radial constraints
    this.radialTexture = this.reglInstance.texture({
      data: this.radialData,
      shape: [size, 1, 4],
      type: 'float',
    })
    
    // Create FBO for radial forces
    this.radialFbo = this.reglInstance.framebuffer({
      color: this.reglInstance.texture({
        data: new Float32Array(this.store.pointsTextureSize * this.store.pointsTextureSize * 4),
        shape: [this.store.pointsTextureSize, this.store.pointsTextureSize, 4],
        type: 'float',
      }),
      depth: false,
      stencil: false,
    })
    
    this.updateRadialTexture()
    this.createPrograms()
  }
  
  public run (): void {
    if (!this.store.pointsTextureSize || !this.runCommand || this.radialGroups.size === 0) return
    
    this.runCommand()
  }
  
  public destroy (): void {
    this.radialFbo?.destroy()
    this.radialTexture?.destroy()
  }
  
  private createPrograms (): void {
    if (!this.store.pointsTextureSize || !this.points.currentPositionFbo) return
    
    const { reglInstance, config, store } = this
    const pointsTextureSize = store.pointsTextureSize
    
    // Radial force command
    this.runCommand = reglInstance({
      frag: `
        precision highp float;
        
        uniform sampler2D positionTexture;
        uniform sampler2D radialTexture;
        uniform float pointsTextureSize;
        uniform float alpha;
        uniform float radialStrength;
        
        varying vec2 textureCoords;
        
        void main() {
          vec2 pointIndex = floor(textureCoords * pointsTextureSize);
          float index = pointIndex.y * pointsTextureSize + pointIndex.x;
          
          // Get current position
          vec4 currentPos = texture2D(positionTexture, textureCoords);
          
          if (currentPos.x == 0.0 && currentPos.y == 0.0) {
            gl_FragColor = vec4(0.0);
            return;
          }
          
          // Get radial constraint for this point
          vec4 radialData = texture2D(radialTexture, vec2(index / (pointsTextureSize * pointsTextureSize), 0.5));
          float centerX = radialData.x;
          float centerY = radialData.y;
          float targetRadius = radialData.z;
          float strength = radialData.w;
          
          // If no radial constraint, return current position
          if (strength == 0.0) {
            gl_FragColor = currentPos;
            return;
          }
          
          // Calculate vector from center to point
          vec2 delta = currentPos.xy - vec2(centerX, centerY);
          float currentRadius = length(delta);
          
          vec2 newPos = currentPos.xy;
          
          if (currentRadius > 0.0) {
            // Calculate radial force
            vec2 direction = normalize(delta);
            float radiusDiff = targetRadius - currentRadius;
            vec2 force = direction * radiusDiff * strength * radialStrength * alpha;
            newPos += force;
          } else if (targetRadius > 0.0) {
            // If point is exactly at center, push it out in a random direction
            float angle = fract(sin(index * 12.9898) * 43758.5453) * 6.28318;
            newPos.x += cos(angle) * targetRadius * strength * radialStrength * alpha;
            newPos.y += sin(angle) * targetRadius * strength * radialStrength * alpha;
          }
          
          gl_FragColor = vec4(newPos, currentPos.z, currentPos.w);
        }
      `,
      vert: `
        precision highp float;
        attribute vec2 position;
        varying vec2 textureCoords;
        
        void main() {
          textureCoords = position * 0.5 + 0.5;
          gl_Position = vec4(position, 0.0, 1.0);
        }
      `,
      attributes: {
        position: [[-1, -1], [1, -1], [-1, 1], [1, 1]],
      },
      uniforms: {
        positionTexture: () => this.points.currentPositionFbo,
        radialTexture: () => this.radialTexture,
        pointsTextureSize: () => pointsTextureSize,
        alpha: () => store.alpha,
        radialStrength: () => config.simulationRadialStrength ?? 0.1,
      },
      primitive: 'triangle strip',
      count: 4,
      framebuffer: () => this.points.previousPositionFbo,
    })
  }
  
  public initPrograms (): void {
    this.createPrograms()
  }
}