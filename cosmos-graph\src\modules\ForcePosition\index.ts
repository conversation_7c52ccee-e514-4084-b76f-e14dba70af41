import regl from 'regl'
import { GraphConfig } from '@/graph/config'
import { Store } from '@/graph/modules/Store'
import { GraphData } from '@/graph/modules/GraphData'
import { Points } from '@/graph/modules/Points'
import { CoreModule } from '@/graph/modules/core-module'

export interface PositionConstraint {
  index: number
  x?: number
  y?: number
  strength?: number
}

export class ForcePosition extends CoreModule {
  private runCommand: regl.DrawCommand | undefined
  private constraintsFbo: regl.Framebuffer2D | undefined
  private constraintsTexture: regl.Texture2D | undefined
  private constraints: Map<number, PositionConstraint> = new Map()
  private constraintData: Float32Array | undefined
  
  public setConstraints (constraints: PositionConstraint[]): void {
    this.constraints.clear()
    constraints.forEach(c => {
      this.constraints.set(c.index, c)
    })
    this.updateConstraintTexture()
  }
  
  public addConstraint (constraint: PositionConstraint): void {
    this.constraints.set(constraint.index, constraint)
    this.updateConstraintTexture()
  }
  
  public removeConstraint (index: number): void {
    this.constraints.delete(index)
    this.updateConstraintTexture()
  }
  
  public clearConstraints (): void {
    this.constraints.clear()
    this.updateConstraintTexture()
  }
  
  private updateConstraintTexture (): void {
    if (!this.store.pointsTextureSize || !this.constraintData) return
    
    // Reset constraint data
    this.constraintData.fill(0)
    
    // Fill constraint data (x, y, strength, enabled)
    this.constraints.forEach((constraint, index) => {
      if (index < this.constraintData.length / 4) {
        const offset = index * 4
        this.constraintData[offset] = constraint.x ?? 0
        this.constraintData[offset + 1] = constraint.y ?? 0
        this.constraintData[offset + 2] = constraint.strength ?? 1.0
        this.constraintData[offset + 3] = (constraint.x !== undefined ? 1 : 0) + (constraint.y !== undefined ? 2 : 0)
      }
    })
    
    // Update texture
    if (this.constraintsTexture) {
      this.constraintsTexture({
        data: this.constraintData,
        shape: [this.store.pointsTextureSize * this.store.pointsTextureSize, 1, 4],
        type: 'float',
      })
    }
  }
  
  public create (): void {
    if (!this.store.pointsTextureSize) return
    
    const size = this.store.pointsTextureSize * this.store.pointsTextureSize
    this.constraintData = new Float32Array(size * 4)
    
    // Create texture for constraints
    this.constraintsTexture = this.reglInstance.texture({
      data: this.constraintData,
      shape: [size, 1, 4],
      type: 'float',
    })
    
    // Create FBO for position constraints
    this.constraintsFbo = this.reglInstance.framebuffer({
      color: this.reglInstance.texture({
        data: new Float32Array(this.store.pointsTextureSize * this.store.pointsTextureSize * 4),
        shape: [this.store.pointsTextureSize, this.store.pointsTextureSize, 4],
        type: 'float',
      }),
      depth: false,
      stencil: false,
    })
    
    this.updateConstraintTexture()
    this.createPrograms()
  }
  
  public run (): void {
    if (!this.store.pointsTextureSize || !this.runCommand || this.constraints.size === 0) return
    
    this.runCommand()
  }
  
  public destroy (): void {
    this.constraintsFbo?.destroy()
    this.constraintsTexture?.destroy()
  }
  
  private createPrograms (): void {
    if (!this.store.pointsTextureSize || !this.points.currentPositionFbo) return
    
    const { reglInstance, config, store } = this
    const pointsTextureSize = store.pointsTextureSize
    
    // Position constraint force
    this.runCommand = reglInstance({
      frag: `
        precision highp float;
        
        uniform sampler2D positionTexture;
        uniform sampler2D constraintsTexture;
        uniform float pointsTextureSize;
        uniform float alpha;
        uniform float positionStrength;
        
        varying vec2 textureCoords;
        
        void main() {
          vec2 pointIndex = floor(textureCoords * pointsTextureSize);
          float index = pointIndex.y * pointsTextureSize + pointIndex.x;
          
          // Get current position
          vec4 currentPos = texture2D(positionTexture, textureCoords);
          
          if (currentPos.x == 0.0 && currentPos.y == 0.0) {
            gl_FragColor = vec4(0.0);
            return;
          }
          
          // Get constraint for this point
          vec4 constraint = texture2D(constraintsTexture, vec2(index / (pointsTextureSize * pointsTextureSize), 0.5));
          float targetX = constraint.x;
          float targetY = constraint.y;
          float strength = constraint.z * positionStrength;
          float flags = constraint.w;
          
          vec2 newPos = currentPos.xy;
          
          // Apply X constraint if enabled (flags & 1)
          if (mod(flags, 2.0) >= 1.0) {
            float deltaX = targetX - currentPos.x;
            newPos.x += deltaX * strength * alpha;
          }
          
          // Apply Y constraint if enabled (flags & 2)
          if (flags >= 2.0) {
            float deltaY = targetY - currentPos.y;
            newPos.y += deltaY * strength * alpha;
          }
          
          gl_FragColor = vec4(newPos, currentPos.z, currentPos.w);
        }
      `,
      vert: `
        precision highp float;
        attribute vec2 position;
        varying vec2 textureCoords;
        
        void main() {
          textureCoords = position * 0.5 + 0.5;
          gl_Position = vec4(position, 0.0, 1.0);
        }
      `,
      attributes: {
        position: [[-1, -1], [1, -1], [-1, 1], [1, 1]],
      },
      uniforms: {
        positionTexture: () => this.points.currentPositionFbo,
        constraintsTexture: () => this.constraintsTexture,
        pointsTextureSize: () => pointsTextureSize,
        alpha: () => store.alpha,
        positionStrength: () => config.simulationPositionStrength ?? 0.1,
      },
      primitive: 'triangle strip',
      count: 4,
      framebuffer: () => this.points.previousPositionFbo,
    })
  }
  
  public initPrograms (): void {
    this.createPrograms()
  }
}