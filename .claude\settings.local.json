{"permissions": {"allow": ["Bash(git pull:*)", "Bash(git config:*)", "Bash(git stash:*)", "Bash(ls:*)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(uv run:*)", "<PERSON><PERSON>(make:*)", "<PERSON><PERSON>(poetry install:*)", "Bash(git add:*)", "mcp__huly-mcp__huly_list_projects", "mcp__huly-mcp__huly_create_project", "mcp__huly-mcp__huly_create_component", "mcp__huly-mcp__huly_create_milestone", "mcp__huly-mcp__huly_bulk_create_issues", "mcp__huly-mcp__huly_list_github_repositories", "mcp__huly-mcp__huly_assign_repository_to_project", "mcp__huly-mcp__huly_bulk_update_issues", "mcp__huly-mcp__huly_update_issue", "mcp__huly-mcp__huly_create_comment", "<PERSON><PERSON>(chmod:*)", "mcp__huly-mcp__huly_create_issue", "<PERSON><PERSON>(docker-compose:*)", "Bash(sudo lsof:*)", "Bash(pip install:*)", "Bash(export FALKORDB_PORT=6379)", "Bash(export NEO4J_PASSWORD=password)", "Bash(export NEO4J_URI=bolt://localhost:7687)", "<PERSON><PERSON>(docker exec:*)", "Bash(docker logs:*)", "Bash(OPENAI_API_KEY=$OPENAI_API_KEY python3 examples/falkordb_ollama_example.py)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(docker rm:*)", "<PERSON><PERSON>(docker run:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(cargo doc:*)", "<PERSON><PERSON>(git clone:*)", "WebFetch(domain:github.com)", "WebFetch(domain:docs.rs)", "Bash(timeout 300 cargo build --release)", "Bash(cargo build:*)", "Bash(cargo:*)", "WebFetch(domain:crates.io)", "WebFetch(domain:docs.falkordb.com)", "<PERSON><PERSON>(docker stop:*)", "Bash(grep:*)", "WebFetch(domain:cosmograph.app)", "Bash(cp:*)", "Bash(rm:*)", "Bash(npm pack:*)", "Bash(tar:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(docker cp:*)", "<PERSON><PERSON>(docker restart:*)", "Bash(find:*)", "Bash(for pkg in juggle-resize-observer d3-array d3-axis d3-brush d3-format d3-scale d3-time d3-time-format escape-string-regexp)", "Bash(do tar -xzf $pkg-*.tgz -C static/vendor/)", "Bash(done)", "Bash(for pkg in d3-array d3-axis d3-brush d3-format d3-scale d3-time d3-time-format escape-string-regexp)", "Bash(git commit:*)", "mcp__huly-mcp__huly_list_issues", "Bash(git rm:*)", "<PERSON><PERSON>(uv:*)", "Bash(pip --version)", "Bash(npm run dev:*)", "Bash(npm run build:*)", "Bash(git push:*)", "Bash(npm run lint)", "mcp__huly-mcp__huly_search_issues", "Bash(npx tsc:*)", "Bash(npx eslint:*)", "Bash(node:*)", "mcp__huly-mcp__huly_list_components", "mcp__huly-mcp__huly_delete_component", "Bash(./start-frontend.sh:*)", "<PERSON><PERSON>(source:*)", "<PERSON><PERSON>(pkill:*)", "Bash(docker build:*)", "Bash(DOCKER_BUILDKIT=1 docker build -t graphiti-server . --progress=plain)", "<PERSON><PERSON>(true)", "Bash(DOCKER_BUILDKIT=1 docker build -t graphiti-server . --no-cache)", "Bash(docker system prune:*)", "Bash(DOCKER_BUILDKIT=1 docker build -t graphiti-server-final . --progress=plain)", "Bash(-e USE_OLLAMA=true )", "Bash(-e OLLAMA_BASE_URL=http://*************:11434/v1 )", "Bash(-e OLLAMA_MODEL=mistral:latest )", "Bash(-e NEO4J_URI=bolt://*************:7687 )", "Bash(-e NEO4J_USER=neo4j )", "Bash(-e NEO4J_PASSWORD=demodemo )", "<PERSON><PERSON>(--name graphiti-olla<PERSON> )", "<PERSON><PERSON>(graphiti-server-final)", "<PERSON><PERSON>(docker start:*)", "<PERSON><PERSON>(docker compose:*)", "Bash(docker compose logs:*)", "<PERSON><PERSON>(docker inspect:*)", "Bash(git checkout:*)", "Bash(git submodule:*)", "mcp__huly-mcp__huly_get_issue_details", "WebFetch(domain:cosmosgl.github.io)", "WebFetch(domain:cosmos.gl)", "mcp__huly-mcp__huly_list_milestones", "Bash(npm install)", "Bash(npm view:*)", "Bash(timeout 30 npm run dev:*)", "WebFetch(domain:next.cosmograph.app)", "Bash(git restore:*)", "<PERSON><PERSON>(docker:*)", "Bash(git reset:*)", "Bash(npm install:*)", "Bash(BACKUP_DIR=\"/opt/stacks/graphiti/backups/falkordb-$(date +%Y%m%d-%H%M%S)\")", "Bash(BACKUP_DIR=\"/opt/stacks/graphiti/backups/falkordb-20250726-153351\")", "Bash(git merge:*)", "Bash(kill:*)", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(time:*)", "<PERSON>sh(sudo cp:*)", "<PERSON><PERSON>(ollama create:*)", "<PERSON><PERSON>(ollama show:*)", "Bash(ollama rm:*)"], "deny": []}}