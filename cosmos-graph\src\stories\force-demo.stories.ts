import type { <PERSON>a, StoryObj } from '@storybook/html'
import { createCosmos } from './create-cosmos'

const meta: Meta = {
  title: 'Forces Demo',
}

export default meta

type Story = StoryObj

export const CollisionDemo: Story = {
  render: () => {
    // Create random points that will collide
    const numPoints = 50
    const pointPositions = new Float32Array(numPoints * 2)
    const pointSizes = new Float32Array(numPoints)
    const pointColors = new Float32Array(numPoints * 4)
    
    for (let i = 0; i < numPoints; i++) {
      // Random positions
      pointPositions[i * 2] = (Math.random() - 0.5) * 200
      pointPositions[i * 2 + 1] = (Math.random() - 0.5) * 200
      
      // Random sizes
      pointSizes[i] = Math.random() * 20 + 5
      
      // Random colors
      pointColors[i * 4] = Math.random() * 255
      pointColors[i * 4 + 1] = Math.random() * 255
      pointColors[i * 4 + 2] = Math.random() * 255
      pointColors[i * 4 + 3] = 255
    }
    
    const { div, graph } = createCosmos({
      pointPositions,
      pointSizes,
      pointColors,
      backgroundColor: '#000000',
      simulationCollision: 1.0,
      simulationGravity: 0.1,
      enableDrag: true,
    })
    
    return div
  },
}

export const PositionConstraintsDemo: Story = {
  render: () => {
    // Create a grid of points with some fixed positions
    const gridSize = 10
    const numPoints = gridSize * gridSize
    const pointPositions = new Float32Array(numPoints * 2)
    
    // Initialize points in a grid
    for (let i = 0; i < gridSize; i++) {
      for (let j = 0; j < gridSize; j++) {
        const index = i * gridSize + j
        pointPositions[index * 2] = (j - gridSize / 2) * 30
        pointPositions[index * 2 + 1] = (i - gridSize / 2) * 30
      }
    }
    
    const { div, graph } = createCosmos({
      pointPositions,
      backgroundColor: '#000000',
      simulationRepulsion: 0.5,
      simulationPositionStrength: 0.3,
      enableDrag: true,
    })
    
    // Set position constraints after graph is created
    const constraints = [
      { index: 0, x: -150, y: -150 },  // Top-left corner
      { index: gridSize - 1, x: 150, y: -150 },  // Top-right corner
      { index: numPoints - gridSize, x: -150, y: 150 },  // Bottom-left corner
      { index: numPoints - 1, x: 150, y: 150 },  // Bottom-right corner
      { index: Math.floor(numPoints / 2), x: 0, y: 0 },  // Center
    ]
    
    graph.setPositionConstraints(constraints)
    
    return div
  },
}

export const RadialForceDemo: Story = {
  render: () => {
    // Create points that will be arranged in radial groups
    const numGroups = 3
    const pointsPerGroup = 20
    const numPoints = numGroups * pointsPerGroup
    const pointPositions = new Float32Array(numPoints * 2)
    const pointColors = new Float32Array(numPoints * 4)
    
    // Initialize points randomly
    for (let i = 0; i < numPoints; i++) {
      pointPositions[i * 2] = (Math.random() - 0.5) * 100
      pointPositions[i * 2 + 1] = (Math.random() - 0.5) * 100
      
      // Color by group
      const groupIndex = Math.floor(i / pointsPerGroup)
      const colors = [
        [255, 0, 0],    // Red
        [0, 255, 0],    // Green
        [0, 0, 255],    // Blue
      ]
      const color = colors[groupIndex]
      pointColors[i * 4] = color[0]
      pointColors[i * 4 + 1] = color[1]
      pointColors[i * 4 + 2] = color[2]
      pointColors[i * 4 + 3] = 255
    }
    
    const { div, graph } = createCosmos({
      pointPositions,
      pointColors,
      backgroundColor: '#000000',
      simulationRadialStrength: 0.2,
      simulationRepulsion: 0.3,
      enableDrag: true,
    })
    
    // Define radial groups
    const radialGroups = []
    for (let g = 0; g < numGroups; g++) {
      const indices = []
      for (let i = 0; i < pointsPerGroup; i++) {
        indices.push(g * pointsPerGroup + i)
      }
      radialGroups.push({
        indices,
        radius: 100 + g * 50,
        strength: 1.0,
      })
    }
    
    graph.setRadialGroups(radialGroups)
    
    return div
  },
}

export const CombinedForcesDemo: Story = {
  render: () => {
    // Demonstrate all forces working together
    const numPoints = 100
    const pointPositions = new Float32Array(numPoints * 2)
    const pointSizes = new Float32Array(numPoints)
    const pointColors = new Float32Array(numPoints * 4)
    const links = []
    
    // Create a connected graph
    for (let i = 0; i < numPoints; i++) {
      pointPositions[i * 2] = (Math.random() - 0.5) * 300
      pointPositions[i * 2 + 1] = (Math.random() - 0.5) * 300
      pointSizes[i] = Math.random() * 10 + 5
      
      // Create some random links
      if (i > 0 && Math.random() < 0.1) {
        links.push(i, Math.floor(Math.random() * i))
      }
      
      // Color based on position
      pointColors[i * 4] = (pointPositions[i * 2] + 150) / 300 * 255
      pointColors[i * 4 + 1] = (pointPositions[i * 2 + 1] + 150) / 300 * 255
      pointColors[i * 4 + 2] = 128
      pointColors[i * 4 + 3] = 255
    }
    
    const { div, graph } = createCosmos({
      pointPositions,
      pointSizes,
      pointColors,
      links: new Float32Array(links),
      backgroundColor: '#000000',
      simulationGravity: 0.05,
      simulationRepulsion: 1.0,
      simulationCollision: 0.8,
      simulationLinkSpring: 0.5,
      simulationRadialStrength: 0.1,
      simulationPositionStrength: 0.2,
      enableDrag: true,
    })
    
    // Fix some corner points
    graph.setPositionConstraints([
      { index: 0, x: -200, y: -200, strength: 0.5 },
      { index: 1, x: 200, y: -200, strength: 0.5 },
      { index: 2, x: -200, y: 200, strength: 0.5 },
      { index: 3, x: 200, y: 200, strength: 0.5 },
    ])
    
    // Create a radial group in the center
    const centerIndices = []
    for (let i = 4; i < 20; i++) {
      centerIndices.push(i)
    }
    graph.setRadialGroups([
      { indices: centerIndices, radius: 50, centerX: 0, centerY: 0 }
    ])
    
    return div
  },
}