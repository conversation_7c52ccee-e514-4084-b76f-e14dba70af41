services:
  graph:
    build:
      context: .
    ports:
      - "8003:8000"
    healthcheck:
      test:
        [
          "CMD",
          "python",
          "-c",
          "import urllib.request; urllib.request.urlopen('http://localhost:8000/healthcheck')",
        ]
      interval: 10s
      timeout: 5s
      retries: 3
    depends_on:
      falkordb:
        condition: service_healthy
      graphiti-centrality-rs:
        condition: service_healthy
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY:-sk-dummy}
      - FALKORDB_HOST=falkordb
      - FALKORDB_PORT=6379
      - FALKORDB_URI=redis://falkordb:6379
      - USE_FALKORDB=true
      - PORT=8000
      # Ollama configuration from .env file
      - USE_OLLAMA=${USE_OLLAMA:-true}
      - OLLAMA_BASE_URL=${OLLAMA_BASE_URL:-http://*************:11434/v1}
      - OLLAMA_MODEL=${OLLAMA_MODEL:-qwen3-graphiti}
      - OLLAMA_EMBEDDING_MODEL=${OLLAMA_EMBEDDING_MODEL:-mxbai-embed-large:latest}
      - USE_OLLAMA_EMBEDDINGS=${USE_OLLAMA_EMBEDDINGS:-true}
      # Centrality service configuration
      - USE_RUST_CENTRALITY=${USE_RUST_CENTRALITY:-true}
      - RUST_CENTRALITY_URL=${RUST_CENTRALITY_URL:-http://graphiti-centrality-rs:3003}
    networks:
      - graphiti_network
    labels:
      - "homepage.group=Knowledge Management"
      - "homepage.name=Graphiti API"
      - "homepage.icon=si-graphql"
      - "homepage.href=http://*************:8000"
      - "homepage.description=Knowledge graph API service"
  falkordb:
    build:
      context: .
      dockerfile: Dockerfile.falkordb-custom
    # command removed as it's handled in the custom entrypoint
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 1s
      timeout: 3s
      retries: 10
      start_period: 3s
    ports:
      - "6389:6379"  # Redis/FalkorDB port (changed from 6379)
      - "3100:3000"  # FalkorDB UI
    volumes:
      - falkordb_data:/data
    environment:
      - REDIS_URL=redis://*************:6389
      - NEXT_PUBLIC_REDIS_URL=redis://*************:6389
      - NEXT_PUBLIC_API_URL=http://*************:6389
    networks:
      - graphiti_network
    labels:
      - "homepage.group=Knowledge Management"
      - "homepage.name=FalkorDB"
      - "homepage.icon=si-redis"
      - "homepage.href=http://*************:3100"
      - "homepage.description=FalkorDB graph database"
  
  graph-visualizer-rust:
    build:
      context: ./graph-visualizer-rust
      dockerfile: Dockerfile
    container_name: graphiti-visualizer-rust
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - FALKORDB_HOST=falkordb
      - FALKORDB_PORT=6379
      - GRAPH_NAME=graphiti_migration
      - RUST_LOG=graph_visualizer=debug,tower_http=debug
    depends_on:
      - falkordb
    networks:
      - graphiti_network
    labels:
      - "homepage.group=Knowledge Management"
      - "homepage.name=Graph Visualizer (Rust)"
      - "homepage.icon=si-rust"
      - "homepage.href=http://*************:3000"
      - "homepage.description=High-performance Rust-based graph visualization"

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: production
    container_name: graphiti-frontend
    restart: unless-stopped
    ports:
      - "8088:80"
    environment:
      - NODE_ENV=production
      - VITE_API_BASE_URL=http://graph-visualizer-rust:3000
      - VITE_WS_URL=ws://graph-visualizer-rust:3000/ws
    depends_on:
      - graph-visualizer-rust
    networks:
      - graphiti_network
    labels:
      - "homepage.group=Knowledge Management"
      - "homepage.name=Graphiti Frontend"
      - "homepage.icon=si-react"
      - "homepage.href=http://*************:8088"
      - "homepage.description=React frontend for graph visualization"

  # graphiti-mcp:
  #   build:
  #     context: ./mcp_server
  #     dockerfile: Dockerfile
  #   container_name: graphiti-mcp
  #   restart: unless-stopped
  #   ports:
  #     - "8001:8000" # MCP Server HTTP endpoint
  #   environment:
  #     - GRAPHITI_API_URL=http://graph:8000
  #     - NEO4J_URI=bolt://falkordb:6379
  #     - NEO4J_USER=default
  #     - NEO4J_PASSWORD=
  #     - OPENAI_API_KEY=${OPENAI_API_KEY:-}
  #     - MODEL_NAME=${MODEL_NAME:-qwen2.5:32b}
  #     - SMALL_MODEL_NAME=${SMALL_MODEL_NAME:-qwen2.5:7b}
  #     - OPENAI_BASE_URL=${OPENAI_BASE_URL:-http://host.docker.internal:11434/v1}
  #     - SEMAPHORE_LIMIT=${SEMAPHORE_LIMIT:-5}
  #   depends_on:
  #     graph:
  #       condition: service_healthy
  #   networks:
  #     - graphiti_network
  #   labels:
  #     - "homepage.group=AI Services"
  #     - "homepage.name=Graphiti MCP Server"
  #     - "homepage.icon=si-openai"
  #     - "homepage.href=http://*************:8001"
  #     - "homepage.description=Model Context Protocol server for AI assistants"
  #   command: ["uv", "run", "graphiti_mcp_server.py", "--transport", "sse"]

  graphiti-centrality-rs:
    build:
      context: ./graphiti-centrality-rs
      dockerfile: Dockerfile
    container_name: graphiti-centrality-rs
    restart: unless-stopped
    ports:
      - "3003:3003" # Rust centrality service
    environment:
      - FALKORDB_HOST=falkordb
      - FALKORDB_PORT=6379
      - GRAPH_NAME=graphiti_migration
      - BIND_ADDR=0.0.0.0:3003
      - RUST_LOG=graphiti_centrality=info,debug
    depends_on:
      falkordb:
        condition: service_healthy
    networks:
      - graphiti_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3003/health"]
      interval: 10s
      timeout: 5s
      retries: 3
    labels:
      - "homepage.group=Knowledge Management"
      - "homepage.name=Centrality Service (Rust)"
      - "homepage.icon=si-rust"
      - "homepage.href=http://*************:3003"
      - "homepage.description=High-performance centrality calculations with native FalkorDB algorithms"

networks:
  graphiti_network:
    driver: bridge

volumes:
  falkordb_data:
