@tailwind base;
@tailwind components;
@tailwind utilities;

/* Graphiti Knowledge Graph Design System - Dark theme with glass-morphism */

@layer base {
  :root {
    /* Dark theme colors for knowledge graph interface */
    --background: 220 13% 9%;
    --foreground: 220 13% 98%;

    --card: 220 13% 11%;
    --card-foreground: 220 13% 98%;

    --popover: 220 13% 11%;
    --popover-foreground: 220 13% 98%;

    /* Primary: Cyan/Teal accent */
    --primary: 174 64% 55%;
    --primary-foreground: 220 13% 9%;

    /* Secondary panels */
    --secondary: 220 13% 15%;
    --secondary-foreground: 220 13% 98%;

    --muted: 220 13% 13%;
    --muted-foreground: 220 13% 65%;

    /* Success: Green */
    --accent: 158 100% 52%;
    --accent-foreground: 220 13% 9%;

    /* Danger: Pink/Red */
    --destructive: 348 83% 67%;
    --destructive-foreground: 220 13% 98%;

    /* Warning: Orange */
    --warning: 30 100% 50%;
    --warning-foreground: 220 13% 9%;

    --border: 220 13% 20%;
    --input: 220 13% 15%;
    --ring: 174 64% 55%;

    --radius: 0.75rem;

    /* Glass-morphism variables */
    --glass-bg: hsla(220, 13%, 11%, 0.8);
    --glass-border: hsla(220, 13%, 25%, 0.3);
    --glass-shadow: 0 8px 32px hsla(220, 13%, 5%, 0.4);
    
    /* Grid pattern for graph background */
    --grid-color: hsla(220, 13%, 25%, 0.1);
    
    /* Node type colors */
    --node-entity: 174 64% 55%;
    --node-episodic: 280 64% 65%;
    --node-agent: 30 100% 60%;
    --node-community: 240 64% 65%;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(174 64% 55%) 0%, hsl(180 64% 65%) 100%);
    --gradient-glass: linear-gradient(135deg, hsla(220, 13%, 15%, 0.8) 0%, hsla(220, 13%, 20%, 0.6) 100%);
    --gradient-panel: linear-gradient(135deg, hsla(220, 13%, 11%, 0.95) 0%, hsla(220, 13%, 13%, 0.85) 100%);

    /* Animations */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-spring: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    
    /* Shadows for depth */
    --shadow-elevation-1: 0 2px 8px hsla(220, 13%, 5%, 0.15);
    --shadow-elevation-2: 0 4px 16px hsla(220, 13%, 5%, 0.25);
    --shadow-elevation-3: 0 8px 32px hsla(220, 13%, 5%, 0.35);
    --shadow-glow: 0 0 40px hsla(174, 64%, 55%, 0.15);

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground overflow-hidden;
  }

  /* Glass-morphism utility classes */
  .glass {
    background: var(--glass-bg);
    backdrop-filter: blur(16px);
    border: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
  }

  .glass-panel {
    background: var(--gradient-panel);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    box-shadow: var(--shadow-elevation-2);
  }

  /* Grid pattern background for graph viewport */
  .graph-grid {
    background-image: radial-gradient(circle, var(--grid-color) 1px, transparent 1px);
    background-size: 20px 20px;
  }

  /* Smooth transitions */
  .transition-smooth {
    transition: var(--transition-smooth);
  }

  .transition-spring {
    transition: var(--transition-spring);
  }

  /* Node type color utilities */
  .node-entity { color: hsl(var(--node-entity)); }
  .node-episodic { color: hsl(var(--node-episodic)); }
  .node-agent { color: hsl(var(--node-agent)); }
  .node-community { color: hsl(var(--node-community)); }

  .bg-node-entity { background-color: hsl(var(--node-entity)); }
  .bg-node-episodic { background-color: hsl(var(--node-episodic)); }
  .bg-node-agent { background-color: hsl(var(--node-agent)); }
  .bg-node-community { background-color: hsl(var(--node-community)); }

  /* Search badge styling with CSS custom properties for dynamic colors */
  .search-badge {
    background-color: hsl(var(--badge-color) / 0.2);
    color: hsl(var(--badge-color));
    border: 1px solid hsl(var(--badge-color) / 0.4);
  }
  
  /* Default search badge for unknown types */
  .search-badge-default {
    background-color: hsl(220 13% 60% / 0.2);
    color: hsl(220 13% 60%);
    border: 1px solid hsl(220 13% 60% / 0.4);
  }

  /* Details panel badge styling with CSS custom properties for dynamic colors */
  .details-badge.details-badge {
    background-color: hsl(var(--badge-color)) !important;
    color: white !important;
    border-color: transparent !important;
  }

  /* Default details badge for unknown types */
  .details-badge-default.details-badge-default {
    background-color: hsl(220 13% 60%) !important;
    color: white !important;
    border-color: transparent !important;
  }

  /* Control panel color indicator with CSS custom properties */
  .control-color-indicator {
    background-color: hsl(var(--indicator-color));
  }

  /* Glow effects */
  .glow-primary {
    box-shadow: var(--shadow-glow);
  }

  /* Scrollbar styling */
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: hsl(var(--muted));
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: hsl(var(--border));
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--primary) / 0.7);
  }

  /* Text overflow utilities for preventing horizontal scroll */
  .overflow-wrap-anywhere {
    overflow-wrap: anywhere;
    word-break: break-word;
  }

  /* Collapsible section animations */
  .CollapsibleContent {
    overflow: hidden;
  }
  .CollapsibleContent[data-state="open"] {
    animation: slideDown 200ms ease-out;
  }
  .CollapsibleContent[data-state="closed"] {
    animation: slideUp 200ms ease-out;
  }

  @keyframes slideDown {
    from {
      height: 0;
      opacity: 0;
    }
    to {
      height: var(--radix-collapsible-content-height);
      opacity: 1;
    }
  }

  @keyframes slideUp {
    from {
      height: var(--radix-collapsible-content-height);
      opacity: 1;
    }
    to {
      height: 0;
      opacity: 0;
    }
  }
}