import regl from 'regl'
import { GraphConfig } from '@/graph/config'
import { Store } from '@/graph/modules/Store'
import { GraphData } from '@/graph/modules/GraphData'
import { Points } from '@/graph/modules/Points'
import { CoreModule } from '@/graph/modules/core-module'
import { readPixels } from '@/graph/helper'

export class ForceCollision extends CoreModule {
  private runCommand: regl.DrawCommand | undefined
  private clearCommand: regl.DrawCommand | undefined
  private radiusFbo: regl.Framebuffer2D | undefined
  private radiusTexture: regl.Texture2D | undefined
  private radius: Float32Array | undefined
  private collisionStrength = 0.7
  
  public create (): void {
    if (!this.store.pointsTextureSize) return
    
    // Initialize radius buffer based on point sizes
    const numPoints = this.graph.pointsNumber ?? 0
    this.radius = new Float32Array(numPoints)
    
    // Use point sizes as collision radii
    const pointSizes = this.graph.pointSizes
    if (pointSizes) {
      for (let i = 0; i < numPoints; i++) {
        this.radius[i] = (pointSizes[i] ?? this.config.pointSize) * 0.5
      }
    } else {
      this.radius.fill(this.config.pointSize * 0.5)
    }
    
    // Create texture for radii
    this.radiusTexture = this.reglInstance.texture({
      data: this.radius,
      shape: [numPoints, 1],
      type: 'float',
    })
    
    // Create FBO for collision detection results
    this.radiusFbo = this.reglInstance.framebuffer({
      color: this.reglInstance.texture({
        data: new Float32Array(this.store.pointsTextureSize * this.store.pointsTextureSize * 4),
        shape: [this.store.pointsTextureSize, this.store.pointsTextureSize, 4],
        type: 'float',
      }),
      depth: false,
      stencil: false,
    })
    
    this.createPrograms()
  }
  
  public run (): void {
    if (!this.store.pointsTextureSize || !this.runCommand) return
    
    this.runCommand()
  }
  
  public destroy (): void {
    this.radiusFbo?.destroy()
    this.radiusTexture?.destroy()
  }
  
  private createPrograms (): void {
    if (!this.store.pointsTextureSize || !this.points.currentPositionFbo || !this.radiusFbo) return
    
    const { reglInstance, config, store } = this
    const pointsTextureSize = store.pointsTextureSize
    
    // Clear command
    this.clearCommand = reglInstance({
      frag: `
        precision highp float;
        void main() {
          gl_FragColor = vec4(0.0);
        }
      `,
      vert: `
        precision highp float;
        attribute vec2 position;
        void main() {
          gl_Position = vec4(position, 0.0, 1.0);
        }
      `,
      attributes: {
        position: [[-1, -1], [1, -1], [-1, 1], [1, 1]],
      },
      primitive: 'triangle strip',
      count: 4,
      framebuffer: this.radiusFbo,
    })
    
    // Collision detection and resolution
    this.runCommand = reglInstance({
      frag: `
        precision highp float;
        
        uniform sampler2D positionTexture;
        uniform sampler2D radiusTexture;
        uniform float pointsTextureSize;
        uniform float alpha;
        uniform float collisionStrength;
        uniform float spaceSize;
        
        varying vec2 textureCoords;
        
        void main() {
          vec2 pointIndex = floor(textureCoords * pointsTextureSize);
          float currentIndex = pointIndex.y * pointsTextureSize + pointIndex.x;
          
          // Get current point position and radius
          vec4 currentPos = texture2D(positionTexture, textureCoords);
          float currentRadius = texture2D(radiusTexture, vec2(currentIndex / ${store.pointsTextureSize}.0, 0.0)).r;
          
          if (currentPos.x == 0.0 && currentPos.y == 0.0) {
            gl_FragColor = vec4(0.0);
            return;
          }
          
          vec2 totalForce = vec2(0.0);
          float collisions = 0.0;
          
          // Check collisions with nearby points (spatial hashing would be more efficient)
          for (float i = 0.0; i < ${store.pointsTextureSize * store.pointsTextureSize}.0; i += 1.0) {
            if (i == currentIndex) continue;
            
            float y = floor(i / pointsTextureSize);
            float x = i - y * pointsTextureSize;
            vec2 coords = (vec2(x, y) + 0.5) / pointsTextureSize;
            
            vec4 otherPos = texture2D(positionTexture, coords);
            if (otherPos.x == 0.0 && otherPos.y == 0.0) continue;
            
            float otherRadius = texture2D(radiusTexture, vec2(i / ${store.pointsTextureSize}.0, 0.0)).r;
            
            vec2 delta = currentPos.xy - otherPos.xy;
            float distance = length(delta);
            float minDistance = currentRadius + otherRadius;
            
            if (distance < minDistance && distance > 0.0) {
              // Calculate repulsion force
              float overlap = minDistance - distance;
              vec2 force = normalize(delta) * overlap * collisionStrength;
              totalForce += force;
              collisions += 1.0;
            }
          }
          
          // Apply force with damping
          if (collisions > 0.0) {
            totalForce = totalForce / collisions * alpha;
            gl_FragColor = vec4(currentPos.x + totalForce.x, currentPos.y + totalForce.y, currentPos.z, currentPos.w);
          } else {
            gl_FragColor = currentPos;
          }
        }
      `,
      vert: `
        precision highp float;
        attribute vec2 position;
        varying vec2 textureCoords;
        
        void main() {
          textureCoords = position * 0.5 + 0.5;
          gl_Position = vec4(position, 0.0, 1.0);
        }
      `,
      attributes: {
        position: [[-1, -1], [1, -1], [-1, 1], [1, 1]],
      },
      uniforms: {
        positionTexture: () => this.points.currentPositionFbo,
        radiusTexture: () => this.radiusTexture,
        pointsTextureSize: () => pointsTextureSize,
        alpha: () => store.alpha,
        collisionStrength: () => this.collisionStrength * (config.simulationCollision ?? 1.0),
        spaceSize: () => store.adjustedSpaceSize,
      },
      primitive: 'triangle strip',
      count: 4,
      framebuffer: () => this.points.previousPositionFbo,
    })
  }
  
  public initPrograms (): void {
    this.createPrograms()
  }
}